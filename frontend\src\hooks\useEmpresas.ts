/**
 * Hook for managing empresas (companies) state and operations
 * Provides CRUD operations, search, filtering, and real-time updates
 */

import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '../lib/api';
import type { 
  Empresa, 
  EmpresaCreate, 
  EmpresaUpdate, 
  EmpresaStats,
  TipoRelacion 
} from '../types/empresa';

interface UseEmpresasParams {
  search?: string;
  tipo_relacion?: TipoRelacion;
  activo?: boolean;
  autoFetch?: boolean;
}

interface UseEmpresasReturn {
  // Data
  empresas: Empresa[];
  empresa: Empresa | null;
  stats: EmpresaStats | null;
  tiposRelacion: string[];
  
  // Loading states
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
  
  // Error states
  error: string | null;
  
  // Operations
  fetchEmpresas: (params?: { search?: string; tipo_relacion?: TipoRelacion; activo?: boolean }) => Promise<void>;
  fetchEmpresaById: (id: string) => Promise<void>;
  createEmpresa: (data: EmpresaCreate) => Promise<Empresa | null>;
  updateEmpresa: (id: string, data: EmpresaUpdate) => Promise<Empresa | null>;
  deleteEmpresa: (id: string) => Promise<boolean>;
  fetchStats: () => Promise<void>;
  fetchTiposRelacion: () => Promise<void>;
  
  // Utilities
  clearError: () => void;
  clearEmpresa: () => void;
  searchEmpresas: (searchTerm: string) => Promise<void>;
}

export const useEmpresas = (params: UseEmpresasParams = {}): UseEmpresasReturn => {
  const { search, tipo_relacion, activo = true, autoFetch = true } = params;
  
  // State
  const [empresas, setEmpresas] = useState<Empresa[]>([]);
  const [empresa, setEmpresa] = useState<Empresa | null>(null);
  const [stats, setStats] = useState<EmpresaStats | null>(null);
  const [tiposRelacion, setTiposRelacion] = useState<string[]>([]);
  
  // Loading states
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [deleting, setDeleting] = useState(false);
  
  // Error state
  const [error, setError] = useState<string | null>(null);
  
  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // Clear empresa
  const clearEmpresa = useCallback(() => {
    setEmpresa(null);
  }, []);
  
  // Fetch empresas list
  const fetchEmpresas = useCallback(async (fetchParams?: { 
    search?: string; 
    tipo_relacion?: TipoRelacion; 
    activo?: boolean 
  }) => {
    try {
      setLoading(true);
      setError(null);
      
      const queryParams = {
        search: fetchParams?.search || search,
        tipo_relacion: fetchParams?.tipo_relacion || tipo_relacion,
        activo: fetchParams?.activo !== undefined ? fetchParams.activo : activo,
      };
      
      // Remove undefined values
      const cleanParams = Object.fromEntries(
        Object.entries(queryParams).filter(([_, value]) => value !== undefined)
      );
      
      const data = await apiClient.empresas.getAll(cleanParams);
      setEmpresas(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error fetching empresas';
      setError(errorMessage);
      console.error('Error fetching empresas:', err);
    } finally {
      setLoading(false);
    }
  }, [search, tipo_relacion, activo]);
  
  // Fetch single empresa by ID
  const fetchEmpresaById = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await apiClient.empresas.getById(id);
      setEmpresa(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error fetching empresa';
      setError(errorMessage);
      console.error('Error fetching empresa:', err);
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Create new empresa
  const createEmpresa = useCallback(async (data: EmpresaCreate): Promise<Empresa | null> => {
    try {
      setCreating(true);
      setError(null);
      
      const newEmpresa = await apiClient.empresas.create(data);
      
      // Add to local state
      setEmpresas(prev => [newEmpresa, ...prev]);
      
      return newEmpresa;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error creating empresa';
      setError(errorMessage);
      console.error('Error creating empresa:', err);
      return null;
    } finally {
      setCreating(false);
    }
  }, []);
  
  // Update empresa
  const updateEmpresa = useCallback(async (id: string, data: EmpresaUpdate): Promise<Empresa | null> => {
    try {
      setUpdating(true);
      setError(null);
      
      const updatedEmpresa = await apiClient.empresas.update(id, data);
      
      // Update local state
      setEmpresas(prev => prev.map(emp => emp.id === id ? updatedEmpresa : emp));
      
      // Update single empresa if it's the current one
      if (empresa?.id === id) {
        setEmpresa(updatedEmpresa);
      }
      
      return updatedEmpresa;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error updating empresa';
      setError(errorMessage);
      console.error('Error updating empresa:', err);
      return null;
    } finally {
      setUpdating(false);
    }
  }, [empresa]);
  
  // Delete empresa
  const deleteEmpresa = useCallback(async (id: string): Promise<boolean> => {
    try {
      setDeleting(true);
      setError(null);
      
      await apiClient.empresas.delete(id);
      
      // Remove from local state
      setEmpresas(prev => prev.filter(emp => emp.id !== id));
      
      // Clear single empresa if it's the deleted one
      if (empresa?.id === id) {
        setEmpresa(null);
      }
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error deleting empresa';
      setError(errorMessage);
      console.error('Error deleting empresa:', err);
      return false;
    } finally {
      setDeleting(false);
    }
  }, [empresa]);
  
  // Fetch stats
  const fetchStats = useCallback(async () => {
    try {
      const data = await apiClient.empresas.getStats();
      setStats(data);
    } catch (err) {
      console.error('Error fetching empresa stats:', err);
    }
  }, []);
  
  // Fetch tipos de relacion
  const fetchTiposRelacion = useCallback(async () => {
    try {
      const data = await apiClient.empresas.getTiposRelacion();
      setTiposRelacion(data.tipos);
    } catch (err) {
      console.error('Error fetching tipos de relacion:', err);
    }
  }, []);
  
  // Search empresas (convenience method)
  const searchEmpresas = useCallback(async (searchTerm: string) => {
    await fetchEmpresas({ search: searchTerm });
  }, [fetchEmpresas]);
  
  // Auto-fetch on mount and when params change
  useEffect(() => {
    if (autoFetch) {
      fetchEmpresas();
    }
  }, [autoFetch, fetchEmpresas]);
  
  // Fetch tipos de relacion on mount
  useEffect(() => {
    fetchTiposRelacion();
  }, [fetchTiposRelacion]);
  
  return {
    // Data
    empresas,
    empresa,
    stats,
    tiposRelacion,
    
    // Loading states
    loading,
    creating,
    updating,
    deleting,
    
    // Error state
    error,
    
    // Operations
    fetchEmpresas,
    fetchEmpresaById,
    createEmpresa,
    updateEmpresa,
    deleteEmpresa,
    fetchStats,
    fetchTiposRelacion,
    
    // Utilities
    clearError,
    clearEmpresa,
    searchEmpresas,
  };
};

export default useEmpresas;
