import React, { useState, useCallback, useRef } from 'react'; // Removed useEffect
import { useChat } from '../../hooks/useChat'; // Corrected import path
import { ChatMessage } from '../../contexts/ChatContext'; // Keep type import
import { useAuth } from '../../hooks/useAuth';
// Removed: import { useAudioRecorder } from 'react-use-audio-recorder';
import { IoMic, IoSend } from 'react-icons/io5';

// Helper function to generate a random 6-digit number for thread_id
const generateThreadId = (): number => {
  return Math.floor(100000 + Math.random() * 900000);
};

const ChatInput: React.FC = () => {
  // --- Contexts ---
  const {
    isProcessingMessage,
    currentThreadId,
    setCurrentThreadId,
    selectedAgentId,
    addMessage,
    startProcessing,
  } = useChat();
  const { user, session } = useAuth();

  // --- State ---
  const [message, setMessage] = useState('');
  const [transcriptionError, setTranscriptionError] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState<boolean>(false); // Local state for recording status
  const mediaRecorderRef = useRef<MediaRecorder | null>(null); // Ref to hold MediaRecorder instance
  const audioChunksRef = useRef<Blob[]>([]); // Ref to hold audio chunks

  // --- Transcription Logic (remains the same) ---
  const handleTranscription = useCallback(async (blob: Blob) => {
    if (!session?.access_token) {
      console.error("No access token found. Cannot transcribe audio.");
      setTranscriptionError("Authentication required for transcription.");
      return;
    }
    setTranscriptionError(null);
    console.log("Sending audio blob for transcription...");

    const formData = new FormData();
    // Determine file extension based on blob type for better compatibility
    const mimeType = blob.type || 'audio/mpeg'; // Default to MP3 MIME type for consistency
    let extension = 'mp3'; // Default to MP3 for best compatibility

    // Use exact MIME type matching to avoid false positives
    if (mimeType === 'audio/mpeg' || mimeType === 'audio/mp3') {
      extension = 'mp3';
    } else if (mimeType === 'audio/wav') {
      extension = 'wav';
    } else if (mimeType === 'audio/mp4') {
      extension = 'mp4';
    } else if (mimeType.startsWith('audio/webm')) {
      extension = 'webm';
    }

    console.log(`Sending audio file: recording.${extension} with MIME type: ${mimeType}`);
    formData.append('audio_file', blob, `recording.${extension}`);

    try {
      // Construct full URL using environment variable
      const transcribeUrl = `${import.meta.env.VITE_API_BASE_URL}/chat/transcribe`;
      console.log("Sending transcription request to:", transcribeUrl);
      const response = await fetch(transcribeUrl, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${session.access_token}` },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        throw new Error(errorData.detail || 'Transcription failed');
      }

      const result = await response.json();
      if (result.text) {
        console.log("Transcription successful:", result.text);
        setMessage((prevMessage) => prevMessage ? `${prevMessage} ${result.text}`.trim() : result.text);
      } else {
        throw new Error("Transcription result did not contain text.");
      }
    } catch (error) {
      console.error("Transcription error:", error);
      setTranscriptionError(error instanceof Error ? error.message : "An unknown error occurred during transcription.");
    }
    // No need to clear blob state here as we don't have one anymore
  }, [session]);

  // --- Send Message Logic (remains mostly the same) ---
  const handleSendMessage = async (e?: React.FormEvent<HTMLFormElement> | React.KeyboardEvent<HTMLTextAreaElement>) => {
    e?.preventDefault();
    const trimmedMessage = message.trim();
    // Add check for selectedAgentId in the guard clause - Removed isProcessingMessage to allow sending while agent is thinking
    if (!trimmedMessage || !user || !selectedAgentId || isRecording) {
      console.warn('Send condition not met:', { message: !!trimmedMessage, user: !!user, selectedAgentId: !!selectedAgentId, isRecording });
      return;
    }
    // ... (rest of send message logic is the same as before) ...
    let threadIdToUse = currentThreadId;
    if (!threadIdToUse) {
      threadIdToUse = generateThreadId();
      setCurrentThreadId(threadIdToUse);
      console.log(`Generated new thread ID: ${threadIdToUse}`);
    }
    const userMessage: ChatMessage = {
      thread_id: threadIdToUse, content: trimmedMessage, type: 'user', from_sender: 'User',
      message_id: -(Date.now()), agent_id: selectedAgentId, user_id: user.id, created_at: new Date().toISOString(),
    };
    addMessage(userMessage);
    startProcessing();
    setMessage('');
    if (!session?.access_token) {
      console.error("No access token found. Cannot send message."); return;
    }
    try {
      // Construct full URL using environment variable
      const messagesUrl = `${import.meta.env.VITE_API_BASE_URL}/chat/messages`;
      console.log("Sending message request to:", messagesUrl);
      const response = await fetch(messagesUrl, {
        method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${session.access_token}` },
        body: JSON.stringify({ thread_id: threadIdToUse, content: trimmedMessage, agent_id: selectedAgentId, user_id: user.id }),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
        console.error(`API Error (${response.status}):`, errorData.detail || 'Unknown error');
      } else { console.log(`Message successfully sent to backend for thread ${threadIdToUse}`); }
    } catch (error) { console.error('Network or other error sending message:', error); }
  };

  // --- Native Audio Recording Logic ---
  const startNativeRecording = async () => {
    setTranscriptionError(null);
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        // Try formats compatible with both OpenAI and Gemini in order of preference
        const compatibleFormats = [
          'audio/mpeg',          // MP3 format - most universal
          'audio/mp3',           // Alternative MP3 MIME type
          'audio/wav',           // WAV format
          'audio/mp4',           // MP4 format
          'audio/webm;codecs=opus', // WebM with Opus (fallback)
          'audio/webm'           // Basic WebM (fallback)
        ];

        let recorder: MediaRecorder | null = null;
        let selectedFormat = 'audio/mpeg'; // Default preference (MP3)

        // Find the first supported format
        for (const format of compatibleFormats) {
          if (MediaRecorder.isTypeSupported(format)) {
            selectedFormat = format;
            recorder = new MediaRecorder(stream, { mimeType: format });
            break;
          }
        }

        // If none of the preferred formats work, use browser default
        if (!recorder) {
          recorder = new MediaRecorder(stream);
          selectedFormat = recorder.mimeType || 'audio/webm';
        }

        console.log(`Using audio format: ${selectedFormat}`);

        mediaRecorderRef.current = recorder;
        audioChunksRef.current = []; // Clear previous chunks

        recorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            audioChunksRef.current.push(event.data);
          }
        };

        recorder.onstop = () => {
          console.log('Native recorder stopped.');
          const audioBlob = new Blob(audioChunksRef.current, { type: recorder.mimeType || selectedFormat });
          handleTranscription(audioBlob); // Trigger transcription with the blob
          // Clean up stream tracks
          stream.getTracks().forEach(track => track.stop());
        };

        recorder.onerror = (event) => {
            console.error("MediaRecorder error:", event);
            setTranscriptionError("Error during recording.");
            setIsRecording(false);
            // Clean up stream tracks
            stream.getTracks().forEach(track => track.stop());
        }

        recorder.start();
        setIsRecording(true);
        console.log('Native recording started.');

      } catch (err) {
        console.error('Error accessing microphone:', err);
        setTranscriptionError('Microphone access denied or error.');
        setIsRecording(false);
      }
    } else {
      setTranscriptionError('Audio recording not supported by this browser.');
    }
  };

  const stopNativeRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop(); // onstop handler will create blob and trigger transcription
      setIsRecording(false);
      // Note: stream tracks are stopped in the onstop handler
    }
  };

  // --- Mic Button Handler ---
  const handleMicClick = () => {
    if (isRecording) {
      stopNativeRecording();
    } else {
      startNativeRecording();
    }
  };

  // --- KeyDown Handler (for Enter key) ---
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e).catch(error => {
        console.error("Error in handleSendMessage triggered by keydown:", error);
      });
    }
  };

  // --- JSX Return ---
  return (
    <div className={`bg-white border-t shadow-lg transition-all duration-300 ${
      isProcessingMessage ? 'border-indigo-200 bg-gradient-to-r from-indigo-50 to-purple-50' : 'border-gray-200'
    }`}>
      {/* Processing indicator */}
      {isProcessingMessage && (
        <div className="px-4 sm:px-6 pt-3 pb-1">
          <div className="flex items-center justify-center space-x-2 text-sm text-indigo-600">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span className="font-medium">Agente pensando... Puedes seguir escribiendo</span>
          </div>
        </div>
      )}
      <form onSubmit={handleSendMessage} className="flex items-end space-x-3 sm:space-x-4 p-4 sm:p-6 max-w-6xl mx-auto">
        {/* Mic Button - Responsive sizing */}
        <button
          type="button"
          onClick={handleMicClick}
          disabled={false} // Always enabled - removed isProcessingMessage to allow recording while agent is thinking
          className={`flex-shrink-0 p-3 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 ${
            isRecording ? 'bg-red-100 hover:bg-red-200 text-red-600 animate-pulse shadow-lg' : 'bg-gray-50 hover:shadow-md'
          }`}
          aria-label={isRecording ? 'Detener grabación' : 'Iniciar grabación'}
        >
          <IoMic size={22} className="sm:w-6 sm:h-6" />
        </button>

      {/* Textarea - Responsive sizing */}
      <div className="flex-1 relative min-w-0">
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={isProcessingMessage ? "El agente está pensando... Puedes seguir escribiendo" : "Escribe tu mensaje..."}
          className="w-full px-4 py-3 border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-indigo-300 focus:border-indigo-300 disabled:bg-gray-100 bg-white text-sm sm:text-base shadow-sm transition-all duration-200 focus:shadow-md"
          rows={window.innerWidth < 640 ? 2 : 3} // Fewer rows on mobile
          disabled={isRecording} // Only disabled while recording, not while processing
        />
        {transcriptionError && (
          <p className="absolute bottom-1 right-2 text-xs text-red-600">{transcriptionError}</p>
        )}
      </div>

      {/* Send Button - Responsive sizing */}
      <button
        type="submit"
        disabled={!message.trim() || !selectedAgentId || isRecording} // Removed isProcessingMessage to allow sending while agent is thinking
        className="flex-shrink-0 p-3 rounded-full bg-indigo-600 text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl disabled:shadow-sm"
        aria-label="Enviar mensaje"
      >
        <IoSend size={20} className="sm:w-5 sm:h-5" />
      </button>
      </form>
    </div>
  );
};

export default ChatInput;