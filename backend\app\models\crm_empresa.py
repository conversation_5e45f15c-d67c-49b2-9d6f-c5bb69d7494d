from pydantic import BaseModel, EmailStr, HttpUrl
from typing import Optional, Literal, List
from uuid import UUID
import datetime # Required for date

class EmpresaBase(BaseModel):
    nombre: str
    nif_cif: Optional[str] = None
    sector: Optional[str] = None
    descripcion: Optional[str] = None
    logo_url: Optional[HttpUrl] = None
    direccion: Optional[str] = None
    direccion_fiscal: Optional[str] = None
    telefono: Optional[str] = None
    email_principal: Optional[EmailStr] = None
    website: Optional[HttpUrl] = None
    tipo_empresa: Optional[str] = None # Consider ENUM if predefined list
    tipo_relacion: Optional[Literal['Cliente', 'Colaborador', 'Otro']] = None
    activo: bool = True
    info_adicional: Optional[str] = None

class EmpresaCreate(EmpresaBase):
    pass

class Empresa(EmpresaBase):
    id: UUID
    fecha_alta: Optional[datetime.date] = None # From DB schema, not in form doc initially
    created_at: datetime.datetime
    updated_at: datetime.datetime

    class Config:
        from_attributes = True

class EmpresaResponse(Empresa):
    pass

# General Tab Models
class DepartmentInfo(BaseModel):
    id: UUID
    nombre: str
    descripcion: Optional[str] = None

class ProjectInfo(BaseModel):
    id: UUID
    nombre: str
    descripcion: Optional[str] = None
    progreso: float = 0.0  # 0-100
    estado: Optional[str] = None
    fecha_inicio: Optional[datetime.date] = None
    fecha_fin_estimada: Optional[datetime.date] = None

class MeetingStats(BaseModel):
    total_reuniones: int = 0
    total_entrevistas: int = 0
    reuniones_vs_entrevistas_ratio: float = 0.0

class ProcessStats(BaseModel):
    total_procesos_clientes: int = 0
    total_tareas_clientes: int = 0

class FindingDistribution(BaseModel):
    tipo: str
    count: int
    percentage: float

class PeoplePanel(BaseModel):
    total_personas_activas: int = 0
    total_personas_entrevistadas: int = 0
    porcentaje_entrevistadas: float = 0.0

class EmpresaGeneralDetails(BaseModel):
    # Basic company info
    empresa: Empresa

    # Main information section
    departamentos: List[DepartmentInfo] = []
    proyectos_relacionados: List[ProjectInfo] = []
    total_trabajadores_activos: int = 0

    # Statistics panel
    estadisticas_reuniones: MeetingStats
    estadisticas_procesos: ProcessStats
    distribucion_hallazgos: List[FindingDistribution] = []
    panel_personas: PeoplePanel
